#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSM to KML Converter
将OpenStreetMap (OSM) 文件转换为KML格式，可在Google Earth等软件中查看
"""

import os
import json
import simplekml
from xml.etree import ElementTree as ET

def parse_osm_to_features(osm_file):
    """
    解析OSM文件并提取地理要素
    """
    try:
        tree = ET.parse(osm_file)
        root = tree.getroot()
        
        features = {
            'nodes': [],
            'ways': [],
            'relations': []
        }
        
        # 解析节点 (nodes)
        for node in root.findall('node'):
            node_data = {
                'id': node.get('id'),
                'lat': float(node.get('lat')),
                'lon': float(node.get('lon')),
                'tags': {}
            }
            
            # 提取标签
            for tag in node.findall('tag'):
                node_data['tags'][tag.get('k')] = tag.get('v')
            
            features['nodes'].append(node_data)
        
        # 解析路径 (ways)
        for way in root.findall('way'):
            way_data = {
                'id': way.get('id'),
                'nodes': [],
                'tags': {}
            }
            
            # 提取节点引用
            for nd in way.findall('nd'):
                way_data['nodes'].append(nd.get('ref'))
            
            # 提取标签
            for tag in way.findall('tag'):
                way_data['tags'][tag.get('k')] = tag.get('v')
            
            features['ways'].append(way_data)
        
        # 解析关系 (relations)
        for relation in root.findall('relation'):
            relation_data = {
                'id': relation.get('id'),
                'members': [],
                'tags': {}
            }
            
            # 提取成员
            for member in relation.findall('member'):
                relation_data['members'].append({
                    'type': member.get('type'),
                    'ref': member.get('ref'),
                    'role': member.get('role')
                })
            
            # 提取标签
            for tag in relation.findall('tag'):
                relation_data['tags'][tag.get('k')] = tag.get('v')
            
            features['relations'].append(relation_data)
        
        return features
        
    except Exception as e:
        print(f"解析OSM文件时出错: {e}")
        return None

def create_kml_from_osm(osm_file, output_file):
    """
    将OSM文件转换为KML格式
    """
    print(f"正在解析OSM文件: {osm_file}")
    features = parse_osm_to_features(osm_file)
    
    if not features:
        print("解析失败!")
        return False
    
    # 创建KML对象
    kml = simplekml.Kml()
    kml.document.name = "OSM数据"
    kml.document.description = "从OpenStreetMap数据转换的KML文件"
    
    # 创建文件夹来组织不同类型的数据
    nodes_folder = kml.newfolder(name="节点 (Nodes)")
    ways_folder = kml.newfolder(name="路径 (Ways)")
    relations_folder = kml.newfolder(name="关系 (Relations)")
    
    # 建立节点ID到坐标的映射
    node_coords = {}
    for node in features['nodes']:
        node_coords[node['id']] = (node['lon'], node['lat'])
    
    # 添加节点到KML
    print(f"正在处理 {len(features['nodes'])} 个节点...")
    for node in features['nodes']:
        if node['tags']:  # 只添加有标签的节点
            point = nodes_folder.newpoint()
            point.name = node['tags'].get('name', f"节点 {node['id']}")
            point.coords = [(node['lon'], node['lat'])]
            
            # 创建描述
            description_parts = [f"节点ID: {node['id']}"]
            for key, value in node['tags'].items():
                description_parts.append(f"{key}: {value}")
            point.description = "<br/>".join(description_parts)
    
    # 添加路径到KML
    print(f"正在处理 {len(features['ways'])} 条路径...")
    for way in features['ways']:
        if len(way['nodes']) >= 2:  # 至少需要2个节点才能构成路径
            # 获取路径的坐标
            coords = []
            for node_ref in way['nodes']:
                if node_ref in node_coords:
                    coords.append(node_coords[node_ref])
            
            if len(coords) >= 2:
                # 判断是否为多边形（首尾节点相同）
                if len(coords) >= 3 and way['nodes'][0] == way['nodes'][-1]:
                    # 创建多边形
                    polygon = ways_folder.newpolygon()
                    polygon.name = way['tags'].get('name', f"区域 {way['id']}")
                    polygon.outerboundaryis = coords
                    
                    # 设置样式
                    polygon.style.polystyle.color = simplekml.Color.changealphaint(100, simplekml.Color.blue)
                    polygon.style.linestyle.color = simplekml.Color.blue
                    polygon.style.linestyle.width = 2
                else:
                    # 创建线段
                    linestring = ways_folder.newlinestring()
                    linestring.name = way['tags'].get('name', f"路径 {way['id']}")
                    linestring.coords = coords
                    
                    # 根据道路类型设置不同颜色
                    highway = way['tags'].get('highway', '')
                    if highway in ['motorway', 'trunk']:
                        linestring.style.linestyle.color = simplekml.Color.red
                        linestring.style.linestyle.width = 4
                    elif highway in ['primary', 'secondary']:
                        linestring.style.linestyle.color = simplekml.Color.orange
                        linestring.style.linestyle.width = 3
                    elif highway in ['residential', 'tertiary']:
                        linestring.style.linestyle.color = simplekml.Color.yellow
                        linestring.style.linestyle.width = 2
                    else:
                        linestring.style.linestyle.color = simplekml.Color.white
                        linestring.style.linestyle.width = 1
                
                # 创建描述
                description_parts = [f"路径ID: {way['id']}"]
                for key, value in way['tags'].items():
                    description_parts.append(f"{key}: {value}")
                
                if 'polygon' in locals():
                    polygon.description = "<br/>".join(description_parts)
                else:
                    linestring.description = "<br/>".join(description_parts)
    
    # 添加关系到KML（简化处理）
    print(f"正在处理 {len(features['relations'])} 个关系...")
    for relation in features['relations']:
        if relation['tags']:
            # 尝试从成员中获取一个坐标点
            coord_found = False
            for member in relation['members']:
                if member['type'] == 'node' and member['ref'] in node_coords:
                    coord = node_coords[member['ref']]
                    coord_found = True
                    break
            
            # 只有找到坐标的关系才创建点
            if coord_found:
                point = relations_folder.newpoint()
                point.name = relation['tags'].get('name', f"关系 {relation['id']}")
                point.coords = [coord]
                
                # 创建描述
                description_parts = [f"关系ID: {relation['id']}"]
                description_parts.append(f"成员数量: {len(relation['members'])}")
                for key, value in relation['tags'].items():
                    description_parts.append(f"{key}: {value}")
                point.description = "<br/>".join(description_parts)
    
    # 保存KML文件
    try:
        kml.save(output_file)
        print(f"✅ 转换成功! KML文件已保存到: {output_file}")
        return True
    except Exception as e:
        print(f"❌ 保存KML文件时出错: {e}")
        return False

def batch_convert_osm_to_kml(input_folder, output_folder=None):
    """
    批量转换文件夹中的所有OSM文件为KML格式
    """
    if not os.path.exists(input_folder):
        print(f"❌ 错误: 找不到文件夹 '{input_folder}'")
        return False

    # 如果没有指定输出文件夹，则在输入文件夹中创建output子文件夹
    if output_folder is None:
        output_folder = os.path.join(input_folder, "output")

    # 创建输出文件夹（如果不存在）
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"📁 创建输出文件夹: {output_folder}")

    # 查找所有OSM文件
    osm_files = []
    for file in os.listdir(input_folder):
        if file.lower().endswith('.osm'):
            osm_files.append(file)

    if not osm_files:
        print(f"❌ 在文件夹 '{input_folder}' 中没有找到OSM文件")
        return False

    print(f"🔍 找到 {len(osm_files)} 个OSM文件:")
    for i, file in enumerate(osm_files, 1):
        print(f"   {i}. {file}")
    print()

    # 批量转换
    successful_conversions = 0
    failed_conversions = 0

    for i, osm_file in enumerate(osm_files, 1):
        input_path = os.path.join(input_folder, osm_file)
        # 生成输出文件名（将.osm替换为.kml）
        kml_filename = os.path.splitext(osm_file)[0] + ".kml"
        output_path = os.path.join(output_folder, kml_filename)

        print(f"🔄 [{i}/{len(osm_files)}] 正在转换: {osm_file}")

        # 显示文件信息
        try:
            file_size = os.path.getsize(input_path)
            print(f"   📊 文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
        except:
            print(f"   ⚠️  无法读取文件大小")

        # 执行转换
        success = create_kml_from_osm(input_path, output_path)

        if success:
            successful_conversions += 1
            try:
                output_size = os.path.getsize(output_path)
                print(f"   ✅ 转换成功! 输出: {kml_filename} ({output_size:,} 字节)")
            except:
                print(f"   ✅ 转换成功! 输出: {kml_filename}")
        else:
            failed_conversions += 1
            print(f"   ❌ 转换失败: {osm_file}")

        print()

    # 显示总结
    print("=" * 60)
    print("📊 批量转换完成!")
    print(f"✅ 成功转换: {successful_conversions} 个文件")
    if failed_conversions > 0:
        print(f"❌ 转换失败: {failed_conversions} 个文件")
    print(f"📁 输出文件夹: {output_folder}")
    print("=" * 60)

    return successful_conversions > 0

def convert_single_file(osm_file, output_file=None):
    """
    转换单个OSM文件
    """
    if output_file is None:
        # 自动生成输出文件名
        output_file = os.path.splitext(osm_file)[0] + ".kml"

    if not os.path.exists(osm_file):
        print(f"❌ 错误: 找不到文件 '{osm_file}'")
        return False

    # 显示文件信息
    file_size = os.path.getsize(osm_file)
    print(f"📁 输入文件: {osm_file}")
    print(f"📊 文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
    print(f"🎯 输出文件: {output_file}")
    print()

    # 执行转换
    success = create_kml_from_osm(osm_file, output_file)

    if success:
        output_size = os.path.getsize(output_file)
        print()
        print("=" * 60)
        print("🎉 转换完成!")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 输出大小: {output_size:,} 字节 ({output_size/1024:.1f} KB)")
        print()
        print("💡 使用建议:")
        print("   • 可以在Google Earth中打开此KML文件")
        print("   • 也可以在Google Maps或其他支持KML的应用中查看")
        print("   • 文件中包含了节点、路径和关系三种要素类型")
        print("=" * 60)
        return True
    else:
        print("❌ 转换失败，请检查错误信息")
        return False

def main():
    """
    主函数
    """
    print("=" * 60)
    print("          OSM to KML 转换器")
    print("=" * 60)

    # 检查是否有kml文件夹
    kml_folder = "kml"
    if os.path.exists(kml_folder):
        print(f"🔍 发现文件夹: {kml_folder}")

        # 检查文件夹中是否有OSM文件
        osm_files = [f for f in os.listdir(kml_folder) if f.lower().endswith('.osm')]

        if osm_files:
            print(f"📁 在 '{kml_folder}' 文件夹中找到 {len(osm_files)} 个OSM文件")
            print("🚀 开始批量转换...")
            print()
            batch_convert_osm_to_kml(kml_folder)
        else:
            print(f"❌ 在 '{kml_folder}' 文件夹中没有找到OSM文件")
    else:
        # 回退到单文件模式
        print("📁 未找到kml文件夹，尝试转换单个文件...")
        osm_file = "map.osm"
        convert_single_file(osm_file)

if __name__ == "__main__":
    main() 