#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KML坐标提取器
从KML文件中提取所有点位坐标并转换为表格格式
"""

import xml.etree.ElementTree as ET
import pandas as pd
import sys
import os

def extract_coordinates_from_kml(kml_file_path):
    """
    从KML文件中提取所有点位坐标
    
    Args:
        kml_file_path (str): KML文件路径
        
    Returns:
        list: 包含坐标信息的字典列表
    """
    try:
        # 解析KML文件
        tree = ET.parse(kml_file_path)
        root = tree.getroot()
        
        # KML命名空间
        namespace = {'kml': 'http://www.opengis.net/kml/2.2'}
        
        # 存储所有点位信息
        points_data = []
        
        # 查找所有Placemark元素
        placemarks = root.findall('.//kml:Placemark', namespace)
        
        print(f"找到 {len(placemarks)} 个地标点")
        
        for i, placemark in enumerate(placemarks, 1):
            try:
                # 获取名称
                name_elem = placemark.find('kml:name', namespace)
                name = name_elem.text if name_elem is not None else f"未命名点位_{i}"
                
                # 获取描述
                desc_elem = placemark.find('kml:description', namespace)
                description = desc_elem.text if desc_elem is not None else ""
                
                # 获取坐标
                point_elem = placemark.find('.//kml:Point', namespace)
                if point_elem is not None:
                    coord_elem = point_elem.find('kml:coordinates', namespace)
                    if coord_elem is not None and coord_elem.text:
                        # 解析坐标字符串 (经度,纬度,高度)
                        coord_text = coord_elem.text.strip()
                        coords = coord_text.split(',')
                        
                        if len(coords) >= 2:
                            longitude = float(coords[0])
                            latitude = float(coords[1])
                            altitude = float(coords[2]) if len(coords) > 2 else 0.0
                            
                            # 添加到数据列表
                            points_data.append({
                                '序号': i,
                                '名称': name,
                                '描述': description,
                                '经度': longitude,
                                '纬度': latitude,
                                '高度': altitude,
                                '坐标字符串': coord_text
                            })
                            
                            if i % 100 == 0:
                                print(f"已处理 {i} 个点位...")
                                
            except Exception as e:
                print(f"处理第 {i} 个点位时出错: {e}")
                continue
        
        return points_data
        
    except Exception as e:
        print(f"解析KML文件时出错: {e}")
        return []

def save_to_excel(points_data, output_file):
    """
    将坐标数据保存为Excel文件
    
    Args:
        points_data (list): 坐标数据列表
        output_file (str): 输出文件路径
    """
    try:
        df = pd.DataFrame(points_data)
        
        # 保存为Excel文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='坐标数据', index=False)
            
            # 获取工作表对象以调整列宽
            worksheet = writer.sheets['坐标数据']
            
            # 调整列宽
            column_widths = {
                'A': 8,   # 序号
                'B': 30,  # 名称
                'C': 20,  # 描述
                'D': 12,  # 经度
                'E': 12,  # 纬度
                'F': 10,  # 高度
                'G': 25   # 坐标字符串
            }
            
            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width
        
        print(f"数据已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")

def save_to_csv(points_data, output_file):
    """
    将坐标数据保存为CSV文件
    
    Args:
        points_data (list): 坐标数据列表
        output_file (str): 输出文件路径
    """
    try:
        df = pd.DataFrame(points_data)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存CSV文件时出错: {e}")

def print_summary(points_data):
    """
    打印数据摘要信息
    
    Args:
        points_data (list): 坐标数据列表
    """
    if not points_data:
        print("没有找到任何坐标数据")
        return
    
    df = pd.DataFrame(points_data)
    
    print("\n=== 数据摘要 ===")
    print(f"总点位数量: {len(points_data)}")
    print(f"经度范围: {df['经度'].min():.6f} ~ {df['经度'].max():.6f}")
    print(f"纬度范围: {df['纬度'].min():.6f} ~ {df['纬度'].max():.6f}")
    print(f"高度范围: {df['高度'].min():.3f} ~ {df['高度'].max():.3f}")
    
    print("\n=== 前5个点位示例 ===")
    for i, point in enumerate(points_data[:5]):
        print(f"{point['序号']}. {point['名称']}")
        print(f"   坐标: ({point['经度']:.6f}, {point['纬度']:.6f}, {point['高度']:.3f})")
        print()

def main():
    """主函数"""
    # 输入文件路径
    kml_file = "台-c增.kml"
    
    # 检查文件是否存在
    if not os.path.exists(kml_file):
        print(f"错误: 找不到文件 {kml_file}")
        return
    
    print(f"开始处理KML文件: {kml_file}")
    
    # 提取坐标数据
    points_data = extract_coordinates_from_kml(kml_file)
    
    if not points_data:
        print("没有提取到任何坐标数据")
        return
    
    # 打印摘要信息
    print_summary(points_data)
    
    # 生成输出文件名
    base_name = os.path.splitext(kml_file)[0]
    excel_file = f"{base_name}_坐标表格.xlsx"
    csv_file = f"{base_name}_坐标表格.csv"
    
    # 保存为Excel和CSV格式
    save_to_excel(points_data, excel_file)
    save_to_csv(points_data, csv_file)
    
    print(f"\n处理完成! 共提取了 {len(points_data)} 个点位的坐标数据")

if __name__ == "__main__":
    main()
